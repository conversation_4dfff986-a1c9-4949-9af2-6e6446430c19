{"allContent": {"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/docs/introduction/docs", "tagsPath": "/docs/introduction/docs/tags", "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs", "editUrlLocalized": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/Daemongodwiz/multimind-dev/multimind-sdk/multimind-docs/sidebars.js", "contentPath": "/Users/<USER>/Daemongodwiz/multimind-dev/multimind-sdk/multimind-docs/docs", "contentPathLocalized": "/Users/<USER>/Daemongodwiz/multimind-dev/multimind-sdk/multimind-docs/i18n/en/docusaurus-plugin-content-docs/current", "docs": [{"id": "api/authentication", "title": "Authentication", "description": "MultiMind SDK supports both API key and JWT authentication for secure access to the RAG API and other services.", "source": "@site/docs/api/authentication.md", "sourceDirName": "api", "slug": "/api/authentication", "permalink": "/docs/introduction/docs/api/authentication", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/api/authentication.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "api/client-library", "title": "Python Client Library", "description": "The MultiMind Python client makes it easy to interact with the RAG API and other SDK features from your own code.", "source": "@site/docs/api/client-library.md", "sourceDirName": "api", "slug": "/api/client-library", "permalink": "/docs/introduction/docs/api/client-library", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/api/client-library.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "api/index", "title": "API Reference", "description": "This section provides detailed documentation for the MultiMind SDK APIs.", "source": "@site/docs/api/index.md", "sourceDirName": "api", "slug": "/api/", "permalink": "/docs/introduction/docs/api/", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/api/index.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "api/rag-api", "title": "RAG API Reference", "description": "The Retrieval-Augmented Generation (RAG) module provides tools for document ingestion, embedding, retrieval, and generation.", "source": "@site/docs/api/rag-api.md", "sourceDirName": "api", "slug": "/api/rag-api", "permalink": "/docs/introduction/docs/api/rag-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/api/rag-api.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "architecture", "title": "Architecture", "description": "MultiMind SDK is designed with modularity and extensibility in mind. The architecture enables seamless integration of models, agents, RAG, and orchestration workflows.", "source": "@site/docs/architecture.md", "sourceDirName": ".", "slug": "/architecture", "permalink": "/docs/introduction/docs/architecture", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/architecture.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Getting Started", "permalink": "/docs/introduction/docs/getting-started"}, "next": {"title": "Integration Guide", "permalink": "/docs/introduction/docs/integration-guide"}}, {"id": "architecture/index", "title": "Architecture", "description": "Learn about the architecture of MultiMind SDK:", "source": "@site/docs/architecture/index.md", "sourceDirName": "architecture", "slug": "/architecture/", "permalink": "/docs/introduction/docs/architecture/", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/architecture/index.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "architecture/overview", "title": "Architecture Overview", "description": "MultiMind SDK is designed for modularity, extensibility, and developer productivity. The architecture enables seamless integration of models, agents, RAG, and orchestration workflows.", "source": "@site/docs/architecture/overview.md", "sourceDirName": "architecture", "slug": "/architecture/overview", "permalink": "/docs/introduction/docs/architecture/overview", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/architecture/overview.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "contributing", "title": "Contributing", "description": "Thank you for your interest in contributing to MultiMind SDK!", "source": "@site/docs/contributing.md", "sourceDirName": ".", "slug": "/contributing", "permalink": "/docs/introduction/docs/contributing", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/contributing.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Integration Guide", "permalink": "/docs/introduction/docs/integration-guide"}}, {"id": "examples/basic-agent", "title": "Basic Agent Example", "description": "This example demonstrates how to create and use a basic agent with MultiMind SDK.", "source": "@site/docs/examples/basic-agent.md", "sourceDirName": "examples", "slug": "/examples/basic-agent", "permalink": "/docs/introduction/docs/examples/basic-agent", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/examples/basic-agent.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "features/advanced-features", "title": "Advanced Features", "description": "Unlock advanced AI capabilities with MultiMind SDK:", "source": "@site/docs/features/advanced-features.md", "sourceDirName": "features", "slug": "/features/advanced-features", "permalink": "/docs/introduction/docs/features/advanced-features", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/features/advanced-features.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "features/core-features", "title": "Core Features", "description": "MultiMind SDK offers a rich set of features for modern AI development:", "source": "@site/docs/features/core-features.md", "sourceDirName": "features", "slug": "/features/core-features", "permalink": "/docs/introduction/docs/features/core-features", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/features/core-features.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "features/implementation-status", "title": "Implementation Status", "description": "| Feature/Functionality         | Status      |", "source": "@site/docs/features/implementation-status.md", "sourceDirName": "features", "slug": "/features/implementation-status", "permalink": "/docs/introduction/docs/features/implementation-status", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/features/implementation-status.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "features/index", "title": "Features", "description": "Welcome to the MultiMind SDK Features section. Here you'll find an overview of all the core and advanced features that make MultiMind powerful and flexible.", "source": "@site/docs/features/index.md", "sourceDirName": "features", "slug": "/features/", "permalink": "/docs/introduction/docs/features/", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/features/index.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "getting-started", "title": "Getting Started", "description": "Prerequisites", "source": "@site/docs/getting-started.md", "sourceDirName": ".", "slug": "/getting-started", "permalink": "/docs/introduction/docs/getting-started", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/getting-started.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Introduction", "permalink": "/docs/introduction/docs/introduction"}, "next": {"title": "Architecture", "permalink": "/docs/introduction/docs/architecture"}}, {"id": "getting-started/index", "title": "Getting Started", "description": "Start your journey with MultiMind SDK:", "source": "@site/docs/getting-started/index.md", "sourceDirName": "getting-started", "slug": "/getting-started/", "permalink": "/docs/introduction/docs/getting-started/", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/getting-started/index.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "getting-started/installation", "title": "Installation", "description": "Basic Installation", "source": "@site/docs/getting-started/installation.md", "sourceDirName": "getting-started", "slug": "/getting-started/installation", "permalink": "/docs/introduction/docs/getting-started/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/getting-started/installation.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "getting-started/quickstart", "title": "Quickstart", "description": "Get started with MultiMind SDK in minutes!", "source": "@site/docs/getting-started/quickstart.md", "sourceDirName": "getting-started", "slug": "/getting-started/quickstart", "permalink": "/docs/introduction/docs/getting-started/quickstart", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/getting-started/quickstart.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "guides/basic-usage", "title": "Basic Usage Guide", "description": "This guide walks you through the most common tasks with MultiMind SDK.", "source": "@site/docs/guides/basic-usage.md", "sourceDirName": "guides", "slug": "/guides/basic-usage", "permalink": "/docs/introduction/docs/guides/basic-usage", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/guides/basic-usage.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "integration-guide", "title": "Integration Guide", "description": "This guide demonstrates how to integrate MultiMind SDK into your own Python projects or APIs.", "source": "@site/docs/integration-guide.md", "sourceDirName": ".", "slug": "/integration-guide", "permalink": "/docs/introduction/docs/integration-guide", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/integration-guide.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Architecture", "permalink": "/docs/introduction/docs/architecture"}, "next": {"title": "Contributing", "permalink": "/docs/introduction/docs/contributing"}}, {"id": "intro", "title": "intro", "description": "MultiMind SDK is a unified, developer-friendly toolkit for building, fine-tuning, and deploying advanced AI agents and Retrieval-Augmented Generation (RAG) systems. Whether you're a researcher, engineer, or enterprise developer, MultiMind SDK provides a consistent interface and powerful abstractions to accelerate your AI projects.", "source": "@site/docs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/docs/introduction/docs/intro", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/intro.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "introduction", "title": "Introduction", "description": "Welcome to the MultiMind SDK documentation.", "source": "@site/docs/introduction.md", "sourceDirName": ".", "slug": "/introduction", "permalink": "/docs/introduction/docs/introduction", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/main/website/docs/introduction.md", "tags": [], "version": "current", "lastUpdatedAt": 1539502055000, "frontMatter": {}, "sidebar": "tutorialSidebar", "next": {"title": "Getting Started", "permalink": "/docs/introduction/docs/getting-started"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "introduction"}, {"type": "doc", "id": "getting-started"}, {"type": "doc", "id": "architecture"}, {"type": "doc", "id": "integration-guide"}, {"type": "doc", "id": "contributing"}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [], "blogListPaginated": [], "blogTags": {}, "blogTagsListPath": "/docs/introduction/blog/tags"}}, "docusaurus-plugin-content-pages": {"default": null}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-theme-search-algolia": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}