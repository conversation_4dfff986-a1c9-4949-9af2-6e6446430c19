# Environment variables
.env
.env.*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
.env/
.venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/
multimind.log

# Model files and caches
models/
.cache/
*.bin
*.pt
*.pth
*.onnx
*.gguf

# Documentation
docs/_build/
site/

# Distribution
dist/
build/
*.egg-info/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Local development
*.local
local_settings.py
db.sqlite3
db.sqlite3-journal

# Temporary files
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# Project specific
multimind-docs/node_modules/
multimind-docs/.next/
multimind-docs/out/ 