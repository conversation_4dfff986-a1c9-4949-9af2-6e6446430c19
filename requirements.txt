accelerate==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.2
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.52.1
anyio==4.9.0
asgiref==3.8.1
async-timeout==5.0.1
attrs==25.3.0
backoff==2.2.1
backports.tarfile==1.2.0
bcrypt==4.3.0
bitsandbytes==0.46.0
black==25.1.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
chromadb==1.0.10
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
cryptography==45.0.2
datasets==3.6.0
Deprecated==1.2.18
dill==0.3.8
distro==1.9.0
docutils==0.21.2
durationpy==0.10
EbookLib==0.19
ecdsa==0.19.1
eval_type_backport==0.2.2
exceptiongroup==1.3.0
faiss-cpu==1.11.0
fastapi==0.115.9
filelock==3.18.0
flatbuffers==25.2.10
frozenlist==1.6.0
fsspec==2025.3.0
google-auth==2.40.2
googleapis-common-protos==1.70.0
greenlet==3.2.2
grpcio==1.71.0
h11==0.16.0
hippo-api==1.1.0rc3
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.32.0
humanfriendly==10.0
id==1.5.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
isort==6.0.1
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
keyring==25.6.0
kubernetes==32.0.1
lxml==5.4.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
mistralai==1.8.1
mmh3==5.1.0
more-itertools==10.7.0
mpmath==1.3.0
multidict==6.4.4

multiprocess==0.70.16
mypy==1.15.0
mypy_extensions==1.1.0
networkx==3.4.2
nh3==0.2.21
numpy==2.2.6
oauthlib==3.2.2
onnxruntime==1.22.0
openai==1.82.0
opentelemetry-api==1.33.1
opentelemetry-exporter-otlp-proto-common==1.33.1
opentelemetry-exporter-otlp-proto-grpc==1.33.1
opentelemetry-instrumentation==0.54b1
opentelemetry-instrumentation-asgi==0.54b1
opentelemetry-instrumentation-fastapi==0.54b1
opentelemetry-proto==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
opentelemetry-util-http==0.54b1
orjson==3.10.18
overrides==7.7.0
packaging==25.0
pandas==2.2.3
pathspec==0.12.1
pdf2image==1.17.0
peft==0.15.2
pillow==11.2.1
pinecone-client==6.0.0
pinecone-plugin-interface==0.0.7
platformdirs==4.3.8
pluggy==1.6.0
posthog==4.1.0
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.11.5
pydantic_core==2.33.2
Pygments==2.19.1
PyPDF2==3.0.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pytesseract==0.3.13
pytest==8.3.5
pytest-asyncio==1.0.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.5.0
python-multipart==0.0.20
pytz==2025.2
pywin32-ctypes==0.2.3
PyYAML==6.0.2
readme_renderer==44.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3986==2.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
ruff==0.11.11
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.3
sentence-transformers==4.1.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
starlette==0.45.3
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tokenizers==0.21.1
tomli==2.2.1
torch==2.7.0
tqdm==4.67.1
transformers==4.52.3
twine==6.1.0
typer==0.15.4
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.2
watchfiles==1.0.5
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.0
zipp==3.21.0
