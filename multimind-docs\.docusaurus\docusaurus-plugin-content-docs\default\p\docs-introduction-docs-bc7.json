{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Introduction", "href": "/docs/introduction/docs/introduction", "docId": "introduction", "unlisted": false}, {"type": "link", "label": "Getting Started", "href": "/docs/introduction/docs/getting-started", "docId": "getting-started", "unlisted": false}, {"type": "link", "label": "Architecture", "href": "/docs/introduction/docs/architecture", "docId": "architecture", "unlisted": false}, {"type": "link", "label": "Integration Guide", "href": "/docs/introduction/docs/integration-guide", "docId": "integration-guide", "unlisted": false}, {"type": "link", "label": "Contributing", "href": "/docs/introduction/docs/contributing", "docId": "contributing", "unlisted": false}]}, "docs": {"api/authentication": {"id": "api/authentication", "title": "Authentication", "description": "MultiMind SDK supports both API key and JWT authentication for secure access to the RAG API and other services."}, "api/client-library": {"id": "api/client-library", "title": "Python Client Library", "description": "The MultiMind Python client makes it easy to interact with the RAG API and other SDK features from your own code."}, "api/index": {"id": "api/index", "title": "API Reference", "description": "This section provides detailed documentation for the MultiMind SDK APIs."}, "api/rag-api": {"id": "api/rag-api", "title": "RAG API Reference", "description": "The Retrieval-Augmented Generation (RAG) module provides tools for document ingestion, embedding, retrieval, and generation."}, "architecture": {"id": "architecture", "title": "Architecture", "description": "MultiMind SDK is designed with modularity and extensibility in mind. The architecture enables seamless integration of models, agents, RAG, and orchestration workflows.", "sidebar": "tutorialSidebar"}, "architecture/index": {"id": "architecture/index", "title": "Architecture", "description": "Learn about the architecture of MultiMind SDK:"}, "architecture/overview": {"id": "architecture/overview", "title": "Architecture Overview", "description": "MultiMind SDK is designed for modularity, extensibility, and developer productivity. The architecture enables seamless integration of models, agents, RAG, and orchestration workflows."}, "contributing": {"id": "contributing", "title": "Contributing", "description": "Thank you for your interest in contributing to MultiMind SDK!", "sidebar": "tutorialSidebar"}, "examples/basic-agent": {"id": "examples/basic-agent", "title": "Basic Agent Example", "description": "This example demonstrates how to create and use a basic agent with MultiMind SDK."}, "features/advanced-features": {"id": "features/advanced-features", "title": "Advanced Features", "description": "Unlock advanced AI capabilities with MultiMind SDK:"}, "features/core-features": {"id": "features/core-features", "title": "Core Features", "description": "MultiMind SDK offers a rich set of features for modern AI development:"}, "features/implementation-status": {"id": "features/implementation-status", "title": "Implementation Status", "description": "| Feature/Functionality         | Status      |"}, "features/index": {"id": "features/index", "title": "Features", "description": "Welcome to the MultiMind SDK Features section. Here you'll find an overview of all the core and advanced features that make MultiMind powerful and flexible."}, "getting-started": {"id": "getting-started", "title": "Getting Started", "description": "Prerequisites", "sidebar": "tutorialSidebar"}, "getting-started/index": {"id": "getting-started/index", "title": "Getting Started", "description": "Start your journey with MultiMind SDK:"}, "getting-started/installation": {"id": "getting-started/installation", "title": "Installation", "description": "Basic Installation"}, "getting-started/quickstart": {"id": "getting-started/quickstart", "title": "Quickstart", "description": "Get started with MultiMind SDK in minutes!"}, "guides/basic-usage": {"id": "guides/basic-usage", "title": "Basic Usage Guide", "description": "This guide walks you through the most common tasks with MultiMind SDK."}, "integration-guide": {"id": "integration-guide", "title": "Integration Guide", "description": "This guide demonstrates how to integrate MultiMind SDK into your own Python projects or APIs.", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "intro", "description": "MultiMind SDK is a unified, developer-friendly toolkit for building, fine-tuning, and deploying advanced AI agents and Retrieval-Augmented Generation (RAG) systems. Whether you're a researcher, engineer, or enterprise developer, MultiMind SDK provides a consistent interface and powerful abstractions to accelerate your AI projects."}, "introduction": {"id": "introduction", "title": "Introduction", "description": "Welcome to the MultiMind SDK documentation.", "sidebar": "tutorialSidebar"}}}}