from multimind.core.base import BaseLLM
from typing import List, Dict, Any, Optional, Union, AsyncGenerator
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, TextStreamer
from peft import PeftModel
import logging
import yaml
import concurrent.futures
import asyncio

class NonTransformerLLM(BaseLLM):
    """
    Generic template for integrating non-transformer models with the multimind LLM interface.
    Implement the required methods for your specific model.
    """
    def __init__(self, model_name: str, model_instance: Any, **kwargs):
        super().__init__(model_name, **kwargs)
        self.model = model_instance  # This can be any non-transformer model object

    async def generate(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Generate text from the model. Implement this for your specific model.
        """
        raise NotImplementedError("Implement generate for your non-transformer model.")

    async def generate_stream(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate text stream from the model. Optional for non-transformer models.
        """
        yield ""  # Or implement streaming if possible

    async def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Generate chat completion from the model. Optional for non-transformer models.
        """
        raise NotImplementedError("Implement chat for your non-transformer model if applicable.")

    async def chat_stream(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate chat completion stream from the model. Optional for non-transformer models.
        """
        yield ""  # Or implement streaming if possible

    async def embeddings(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[List[float], List[List[float]]]:
        """
        Generate embeddings for the input text. Optional for non-transformer models.
        """
        raise NotImplementedError("Implement embeddings for your non-transformer model if applicable.")

# --- Advanced Non-Transformer Architectures ---

class SSM_LLM(NonTransformerLLM):
    """
    Advanced wrapper for State-Space Models (SSMs) such as S4, Mamba, with all advanced features.
    Plug in your S4/Mamba model and tokenizer as needed.
    """
    def __init__(self, model_name: str, model_instance: Any, tokenizer: Any, adapter_path: Optional[str] = None, device: Optional[str] = None, torch_dtype: Optional[str] = None, device_map: Optional[str] = None, **kwargs):
        super().__init__(model_name, model_instance, **kwargs)
        self.tokenizer = tokenizer
        dtype = getattr(torch, torch_dtype) if torch_dtype else None
        self.model = model_instance.to(device or ("cuda" if torch.cuda.is_available() else "cpu"))
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model.eval()
        self.logger = logging.getLogger("SSM_LLM")
        self.pre_hooks = []
        self.post_hooks = []
        self.adapter_path = adapter_path

    def preprocess_prompt(self, prompt: str) -> str:
        for hook in self.pre_hooks:
            prompt = hook(prompt)
        return prompt
    def postprocess_output(self, output: str) -> str:
        for hook in self.post_hooks:
            output = hook(output)
        return output
    def add_pre_hook(self, hook):
        self.pre_hooks.append(hook)
    def add_post_hook(self, hook):
        self.post_hooks.append(hook)

    def load_adapter(self, adapter_path: str):
        # Implement adapter loading for your SSM model if supported
        self.adapter_path = adapter_path
    def unload_adapter(self):
        # Implement adapter unloading for your SSM model if supported
        self.adapter_path = None

    async def generate_batch(self, prompts: List[str], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> List[str]:
        # Example: batch process prompts (user must implement details for their SSM)
        return [await self.generate(p, temperature=temperature, max_tokens=max_tokens, **kwargs) for p in prompts]

    async def generate_batch_async(self, prompts: List[str], **kwargs) -> List[str]:
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as pool:
            results = await asyncio.gather(*[loop.run_in_executor(pool, lambda p=p: asyncio.run(self.generate(p, **kwargs))) for p in prompts])
        return results

    async def generate_stream(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        # Implement streaming for your SSM model if supported
        yield await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

    async def chat_stream(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = "\n".join([m["content"] for m in messages])
        async for chunk in self.generate_stream(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs):
            yield chunk

    def new_chat_session(self, persona: Optional[str] = None, max_history: int = 10) -> ChatSession:
        return ChatSession(persona=persona, max_history=max_history)

    def log_metric(self, name: str, value: float):
        self.logger.info(f"Metric: {name} = {value}")
    def log_generation(self, prompt: str, output: str):
        self.logger.info(f"Prompt: {prompt}\nOutput: {output}")

    @classmethod
    def from_config(cls, config_path: str):
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return cls(**config)

    async def generate(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> str:
        prompt = self.preprocess_prompt(prompt)
        # User must implement actual SSM inference here
        # Example: output = self.model.generate(self.tokenizer.encode(prompt), ...)
        output = "[SSM output for: " + prompt + "]"
        result = self.postprocess_output(output)
        self.log_generation(prompt, result)
        return result

    async def chat(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, session: Optional[ChatSession] = None, **kwargs) -> str:
        if session is not None:
            for m in messages:
                session.add_message(m["role"], m["content"])
            prompt = session.get_prompt()
        else:
            prompt = "\n".join([m["content"] for m in messages])
        return await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

class MLPOnlyLLM(NonTransformerLLM):
    """
    Wrapper for MLP-Only models (HyperMixer, gMLP, MLP-Mixer).
    Expects a model instance with a generate method or similar interface.
    Plug in your MLP-based model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your MLP-Only model.")

class DiffusionTextLLM(NonTransformerLLM):
    """
    Wrapper for Diffusion Models for text generation.
    Expects a diffusion model instance with a sample/generate method.
    Plug in your diffusion model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your Diffusion Text model.")

class MoELLMMixin(NonTransformerLLM):
    """
    Wrapper for Mixture-of-Experts (MoE) models.
    Expects a gating network and a list of expert models (can be SSMs, MLPs, RNNs, etc.).
    Plug in your MoE model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your Mixture-of-Experts model.")

class PerceiverLLM(NonTransformerLLM):
    """
    Wrapper for Perceiver/Perceiver IO models.
    Expects a model instance with a generate or forward method.
    Plug in your Perceiver model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your Perceiver model.")

# --- Advanced Sequence Model Wrappers ---

class MegaS4LLM(NonTransformerLLM):
    """
    Wrapper for Mega-S4 (Efficient SSM for long-range dependencies).
    Plug in your Mega-S4 model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your Mega-S4 model.")

class LiquidS4LLM(NonTransformerLLM):
    """
    Wrapper for Liquid-S4 (continuous-time SSM variant).
    Plug in your Liquid-S4 model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your Liquid-S4 model.")

class S4DLLM(NonTransformerLLM):
    """
    Wrapper for S4D (diagonal S4 variant).
    Plug in your S4D model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your S4D model.")

class S4NDLLM(NonTransformerLLM):
    """
    Wrapper for S4ND (non-diagonal S4 variant).
    Plug in your S4ND model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your S4ND model.")

class DSSLLM(NonTransformerLLM):
    """
    Wrapper for DSS (Diagonal State Space) models.
    Plug in your DSS model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your DSS model.")

class GSSLLM(NonTransformerLLM):
    """
    Wrapper for GSS (General State Space) models.
    Plug in your GSS model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your GSS model.")

class ChatSession:
    """
    Advanced chat session for memory, context window, persona/system prompt.
    """
    def __init__(self, persona: Optional[str] = None, max_history: int = 10):
        self.persona = persona
        self.max_history = max_history
        self.history = []
    def add_message(self, role: str, content: str):
        self.history.append({"role": role, "content": content})
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]
    def get_prompt(self):
        prompt = (self.persona + "\n") if self.persona else ""
        prompt += "\n".join([m["content"] for m in self.history])
        return prompt

class MambaLLM(NonTransformerLLM):
    """
    Advanced wrapper for HuggingFace/state-spaces Mamba models with all advanced features.
    """
    def __init__(self, model_name: str = "state-spaces/mamba-130m", adapter_path: Optional[str] = None, device: Optional[str] = None, torch_dtype: Optional[str] = None, device_map: Optional[str] = None, **kwargs):
        super().__init__(model_name, None, **kwargs)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        dtype = getattr(torch, torch_dtype) if torch_dtype else None
        self.model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype=dtype, device_map=device_map)
        if adapter_path:
            try:
                self.model = PeftModel.from_pretrained(self.model, adapter_path)
            except Exception:
                pass
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        self.model.eval()
        self.logger = logging.getLogger("MambaLLM")
        self.pre_hooks = []
        self.post_hooks = []
        self.adapter_path = adapter_path

    # --- Pre/Post-processing hooks ---
    def preprocess_prompt(self, prompt: str) -> str:
        for hook in self.pre_hooks:
            prompt = hook(prompt)
        return prompt
    def postprocess_output(self, output: str) -> str:
        for hook in self.post_hooks:
            output = hook(output)
        return output
    def add_pre_hook(self, hook):
        self.pre_hooks.append(hook)
    def add_post_hook(self, hook):
        self.post_hooks.append(hook)

    # --- Adapter hot-swapping ---
    def load_adapter(self, adapter_path: str):
        self.model = PeftModel.from_pretrained(self.model, adapter_path)
        self.adapter_path = adapter_path
    def unload_adapter(self):
        if self.adapter_path:
            # Reload base model
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            self.adapter_path = None

    # --- Batch generation ---
    async def generate_batch(self, prompts: List[str], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> List[str]:
        inputs = self.tokenizer(prompts, return_tensors="pt", padding=True).to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        with torch.no_grad():
            outputs = self.model.generate(**inputs, **gen_kwargs)
        return [self.tokenizer.decode(o, skip_special_tokens=True) for o in outputs]

    # --- Async/parallel batch generation ---
    async def generate_batch_async(self, prompts: List[str], **kwargs) -> List[str]:
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as pool:
            results = await asyncio.gather(*[loop.run_in_executor(pool, lambda p=p: asyncio.run(self.generate(p, **kwargs))) for p in prompts])
        return results

    # --- Streaming generation ---
    async def generate_stream(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = self.preprocess_prompt(prompt)
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        streamer = TextStreamer(self.tokenizer, skip_prompt=True, skip_special_tokens=True)
        with torch.no_grad():
            self.model.generate(**inputs, streamer=streamer, **gen_kwargs)
        # TextStreamer yields to stdout, so for real streaming, use a custom streamer or yield tokens here
        # For now, just yield the full output
        output = self.model.generate(**inputs, **gen_kwargs)
        yield self.tokenizer.decode(output[0], skip_special_tokens=True)

    async def chat_stream(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = "\n".join([m["content"] for m in messages])
        async for chunk in self.generate_stream(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs):
            yield chunk

    # --- Advanced chat memory/history ---
    def new_chat_session(self, persona: Optional[str] = None, max_history: int = 10) -> ChatSession:
        return ChatSession(persona=persona, max_history=max_history)

    # --- Precision management (fp16/bf16) and distributed ---
    # For multi-GPU/distributed, recommend using accelerate/deepspeed externally
    # Example: pass device_map="auto" and torch_dtype="float16" to __init__

    # --- Evaluation/logging hooks ---
    def log_metric(self, name: str, value: float):
        self.logger.info(f"Metric: {name} = {value}")
    def log_generation(self, prompt: str, output: str):
        self.logger.info(f"Prompt: {prompt}\nOutput: {output}")

    # --- Config-driven instantiation ---
    @classmethod
    def from_config(cls, config_path: str):
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return cls(**config)

    # --- Override generate to use hooks and logging ---
    async def generate(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> str:
        prompt = self.preprocess_prompt(prompt)
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        with torch.no_grad():
            output = self.model.generate(**inputs, **gen_kwargs)
        result = self.tokenizer.decode(output[0], skip_special_tokens=True)
        result = self.postprocess_output(result)
        self.log_generation(prompt, result)
        return result

    async def chat(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, session: Optional[ChatSession] = None, **kwargs) -> str:
        if session is not None:
            for m in messages:
                session.add_message(m["role"], m["content"])
            prompt = session.get_prompt()
        else:
            prompt = "\n".join([m["content"] for m in messages])
        return await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

class MoEMambaLLM(NonTransformerLLM):
    """
    Wrapper for MoE-Mamba (Mamba with Mixture-of-Experts layers).
    Plug in your MoE-Mamba model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your MoE-Mamba model.")

class H3LLM(NonTransformerLLM):
    """
    Wrapper for H3 (Hyena Hybrid) models.
    Plug in your H3 model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your H3 model.")

class RetNetLLM(NonTransformerLLM):
    """
    Wrapper for RetNet (Retentive Network) models.
    Plug in your RetNet model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your RetNet model.")

class RWKVLLM(NonTransformerLLM):
    """
    Advanced wrapper for BlinkDL/rwkv-4-pile-169m (HuggingFace) with all advanced features.
    """
    def __init__(self, model_name: str = "BlinkDL/rwkv-4-pile-169m", adapter_path: Optional[str] = None, device: Optional[str] = None, torch_dtype: Optional[str] = None, device_map: Optional[str] = None, **kwargs):
        super().__init__(model_name, None, **kwargs)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        dtype = getattr(torch, torch_dtype) if torch_dtype else None
        self.model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype=dtype, device_map=device_map)
        if adapter_path:
            try:
                self.model = PeftModel.from_pretrained(self.model, adapter_path)
            except Exception:
                pass
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        self.model.eval()
        self.logger = logging.getLogger("RWKVLLM")
        self.pre_hooks = []
        self.post_hooks = []
        self.adapter_path = adapter_path

    def preprocess_prompt(self, prompt: str) -> str:
        for hook in self.pre_hooks:
            prompt = hook(prompt)
        return prompt
    def postprocess_output(self, output: str) -> str:
        for hook in self.post_hooks:
            output = hook(output)
        return output
    def add_pre_hook(self, hook):
        self.pre_hooks.append(hook)
    def add_post_hook(self, hook):
        self.post_hooks.append(hook)

    def load_adapter(self, adapter_path: str):
        self.model = PeftModel.from_pretrained(self.model, adapter_path)
        self.adapter_path = adapter_path
    def unload_adapter(self):
        if self.adapter_path:
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            self.adapter_path = None

    async def generate_batch(self, prompts: List[str], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> List[str]:
        inputs = self.tokenizer(prompts, return_tensors="pt", padding=True).to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        with torch.no_grad():
            outputs = self.model.generate(**inputs, **gen_kwargs)
        return [self.tokenizer.decode(o, skip_special_tokens=True) for o in outputs]

    async def generate_batch_async(self, prompts: List[str], **kwargs) -> List[str]:
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as pool:
            results = await asyncio.gather(*[loop.run_in_executor(pool, lambda p=p: asyncio.run(self.generate(p, **kwargs))) for p in prompts])
        return results

    async def generate_stream(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = self.preprocess_prompt(prompt)
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        streamer = TextStreamer(self.tokenizer, skip_prompt=True, skip_special_tokens=True)
        with torch.no_grad():
            self.model.generate(**inputs, streamer=streamer, **gen_kwargs)
        output = self.model.generate(**inputs, **gen_kwargs)
        yield self.tokenizer.decode(output[0], skip_special_tokens=True)

    async def chat_stream(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = "\n".join([m["content"] for m in messages])
        async for chunk in self.generate_stream(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs):
            yield chunk

    def new_chat_session(self, persona: Optional[str] = None, max_history: int = 10) -> ChatSession:
        return ChatSession(persona=persona, max_history=max_history)

    def log_metric(self, name: str, value: float):
        self.logger.info(f"Metric: {name} = {value}")
    def log_generation(self, prompt: str, output: str):
        self.logger.info(f"Prompt: {prompt}\nOutput: {output}")

    @classmethod
    def from_config(cls, config_path: str):
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return cls(**config)

    async def generate(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> str:
        prompt = self.preprocess_prompt(prompt)
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        gen_kwargs = {"temperature": temperature}
        if max_tokens:
            gen_kwargs["max_new_tokens"] = max_tokens
        with torch.no_grad():
            output = self.model.generate(**inputs, **gen_kwargs)
        result = self.tokenizer.decode(output[0], skip_special_tokens=True)
        result = self.postprocess_output(result)
        self.log_generation(prompt, result)
        return result

    async def chat(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, session: Optional[ChatSession] = None, **kwargs) -> str:
        if session is not None:
            for m in messages:
                session.add_message(m["role"], m["content"])
            prompt = session.get_prompt()
        else:
            prompt = "\n".join([m["content"] for m in messages])
        return await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

class SE3HyenaLLM(NonTransformerLLM):
    """
    Wrapper for SE(3)-Hyena (equivariant Hyena for 3D/spatial tasks).
    Plug in your SE(3)-Hyena model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your SE(3)-Hyena model.")

class TopologicalNNLLM(NonTransformerLLM):
    """
    Wrapper for topological deep learning models (simplicial, hypergraph, cellular, etc.).
    Plug in your topological NN model and tokenizer as needed.
    """
    async def generate(self, prompt: str, **kwargs) -> str:
        raise NotImplementedError("Implement generate for your topological NN model.")

class CustomRNNLLM(NonTransformerLLM):
    """
    Advanced template for custom RNN/MLP models (PyTorch/Keras) with all advanced features.
    """
    def __init__(self, model_instance: Any, tokenizer: Any, device: Optional[str] = None, torch_dtype: Optional[str] = None, **kwargs):
        super().__init__("custom-rnn", model_instance, **kwargs)
        self.tokenizer = tokenizer
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model = model_instance.to(self.device)
        self.model.eval()
        self.logger = logging.getLogger("CustomRNNLLM")
        self.pre_hooks = []
        self.post_hooks = []
        self.adapter_path = None

    def preprocess_prompt(self, prompt: str) -> str:
        for hook in self.pre_hooks:
            prompt = hook(prompt)
        return prompt
    def postprocess_output(self, output: str) -> str:
        for hook in self.post_hooks:
            output = hook(output)
        return output
    def add_pre_hook(self, hook):
        self.pre_hooks.append(hook)
    def add_post_hook(self, hook):
        self.post_hooks.append(hook)

    def load_adapter(self, adapter_path: str):
        # Implement adapter loading for your RNN/MLP model if supported
        self.adapter_path = adapter_path
    def unload_adapter(self):
        # Implement adapter unloading for your RNN/MLP model if supported
        self.adapter_path = None

    async def generate_batch(self, prompts: List[str], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> List[str]:
        return [await self.generate(p, temperature=temperature, max_tokens=max_tokens, **kwargs) for p in prompts]

    async def generate_batch_async(self, prompts: List[str], **kwargs) -> List[str]:
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as pool:
            results = await asyncio.gather(*[loop.run_in_executor(pool, lambda p=p: asyncio.run(self.generate(p, **kwargs))) for p in prompts])
        return results

    async def generate_stream(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        yield await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

    async def chat_stream(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> AsyncGenerator[str, None]:
        prompt = "\n".join([m["content"] for m in messages])
        async for chunk in self.generate_stream(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs):
            yield chunk

    def new_chat_session(self, persona: Optional[str] = None, max_history: int = 10) -> ChatSession:
        return ChatSession(persona=persona, max_history=max_history)

    def log_metric(self, name: str, value: float):
        self.logger.info(f"Metric: {name} = {value}")
    def log_generation(self, prompt: str, output: str):
        self.logger.info(f"Prompt: {prompt}\nOutput: {output}")

    @classmethod
    def from_config(cls, config_path: str):
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return cls(**config)

    async def generate(self, prompt: str, temperature: float = 0.7, max_tokens: Optional[int] = None, **kwargs) -> str:
        prompt = self.preprocess_prompt(prompt)
        # Example: encode, run model, decode (user must implement details)
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        with torch.no_grad():
            output_ids = self.model.generate(input_ids, max_length=max_tokens or 32, temperature=temperature)
        result = self.tokenizer.decode(output_ids[0], skip_special_tokens=True)
        result = self.postprocess_output(result)
        self.log_generation(prompt, result)
        return result

    async def chat(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: Optional[int] = None, session: Optional[ChatSession] = None, **kwargs) -> str:
        if session is not None:
            for m in messages:
                session.add_message(m["role"], m["content"])
            prompt = session.get_prompt()
        else:
            prompt = "\n".join([m["content"] for m in messages])
        return await self.generate(prompt, temperature=temperature, max_tokens=max_tokens, **kwargs)

# --- Adapter management for per-user/session/tool injection ---
class AdapterManager:
    """
    Manages adapters per user/session/tool. Used by advanced LLM wrappers for dynamic LoRA/PEFT injection.
    """
    def __init__(self):
        self.adapters = {}  # key -> adapter_path
    def set_adapter(self, key, adapter_path):
        self.adapters[key] = adapter_path
    def get_adapter(self, key):
        return self.adapters.get(key)
    def remove_adapter(self, key):
        if key in self.adapters:
            del self.adapters[key]

# Patch advanced LLMs to support per-user/session/tool adapter injection
for _LLM in [MambaLLM, H3LLM, RWKVLLM, SSM_LLM, CustomRNNLLM]:
    _LLM.adapter_manager = AdapterManager()
    def load_adapter_for(self, key, adapter_path):
        self.adapter_manager.set_adapter(key, adapter_path)
    def unload_adapter_for(self, key):
        self.adapter_manager.remove_adapter(key)
    def get_active_adapter(self, key):
        return self.adapter_manager.get_adapter(key)
    _LLM.load_adapter_for = load_adapter_for
    _LLM.unload_adapter_for = unload_adapter_for
    _LLM.get_active_adapter = get_active_adapter
    # Patch generate/chat to use adapter if set for key
    orig_generate = _LLM.generate
    async def generate_with_adapter(self, prompt, *args, adapter_key=None, **kwargs):
        adapter_path = self.get_active_adapter(adapter_key) if adapter_key else None
        if adapter_path:
            self.model = PeftModel.from_pretrained(self.model, adapter_path)
        return await orig_generate(self, prompt, *args, **kwargs)
    _LLM.generate = generate_with_adapter
    orig_chat = _LLM.chat
    async def chat_with_adapter(self, messages, *args, adapter_key=None, **kwargs):
        adapter_path = self.get_active_adapter(adapter_key) if adapter_key else None
        if adapter_path:
            self.model = PeftModel.from_pretrained(self.model, adapter_path)
        return await orig_chat(self, messages, *args, **kwargs)
    _LLM.chat = chat_with_adapter 