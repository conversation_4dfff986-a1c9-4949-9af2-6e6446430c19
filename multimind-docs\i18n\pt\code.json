{"theme.ErrorPageContent.title": {"message": "Esta página deu erro.", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "Volte para o topo", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "Arquivo", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "Arquivo", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "Navegação da página de listagem do blog", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "<PERSON><PERSON><PERSON><PERSON> mais novo", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "<PERSON><PERSON><PERSON><PERSON> mais antigo", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "Navegação da página de postagem do blog", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "Postagem mais nova", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "Postagem mais antiga", "description": "The blog post button label to navigate to the older/next post"}, "theme.tags.tagsPageLink": {"message": "Ver todas os Marcadores", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "Alterar entre os modos claro e escuro (modo {mode} ativado)", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "modo escuro", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "modo claro", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "Breadcrumbs", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "1 item|{count} items", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "Páginas de documentação", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "Anterior", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "Próxima", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "Um documento selecionado|{count} documentos selecionados", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged} com \"{tagName}\"", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "Versão: {versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "Esta é uma documentação não lançada para {siteTitle} {versionLabel}.", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "Esta é a documentação para {siteTitle} {versionLabel}, que não é mais mantida ativamente.", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "Para a documentação atualizada, veja: {latestVersionLink} ({versionLabel}).", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "<PERSON>lt<PERSON> versão", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "Editar essa p<PERSON>gina", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "<PERSON> direto para {heading}", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": " em {date}", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": " por {user}", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "Última atualização {atDate}{byUser}", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "Versõ<PERSON>", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "Página não encontrada", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "Marcadores:", "description": "The label alongside a tag list"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The ARIA label for close button of announcement bar"}, "theme.admonition.caution": {"message": "cuidado", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "perigo", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "info", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "nota", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "dica", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "atenção", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.blog.sidebar.navAriaLabel": {"message": "Blog recent posts navigation", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "Copiado", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "Copiar código para a área de transferência", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "Copiar", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "Alternar quebra de linha", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "Expandir a categoria lateral '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "Fechar a categoria lateral '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "Main", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "Linguagens", "description": "The label for the mobile language switcher dropdown"}, "theme.NotFound.p1": {"message": "Não foi possível encontrar o que você está procurando.", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "Entre em contato com o proprietário do site que lhe trouxe para cá e lhe informe que o link está quebrado.", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "<PERSON><PERSON>", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "Ler mais sobre {title}", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "Leitura de um minuto|Leitura de {readingTime} minutos", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "Página Inicial", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "<PERSON><PERSON><PERSON>", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "Docs sidebar", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON> de <PERSON>", "description": "The ARIA label for close button of mobile sidebar"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "Alternar a barra de navegação", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← Voltar para o menu principal", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.expandButtonTitle": {"message": "Expandir painel lateral", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "Expandir painel lateral", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.SearchPage.documentsFound.plurals": {"message": "Um documento encontrado|{count} documentos encontrados", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.existingResultsTitle": {"message": "Resultado da busca por \"{query}\"", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "Busca da documentação", "description": "The search page title for empty query"}, "theme.SearchPage.inputPlaceholder": {"message": "Digite sua busca aqui", "description": "The placeholder for search page input"}, "theme.SearchPage.inputLabel": {"message": "Buscar", "description": "The ARIA label for search page input"}, "theme.SearchPage.algoliaLabel": {"message": "Busca feita por Algolia", "description": "The ARIA label for Algolia mention"}, "theme.SearchPage.noResultsText": {"message": "Nenhum resultado foi encontrado", "description": "The paragraph for empty search result"}, "theme.SearchPage.fetchingNewResults": {"message": "Trazendo novos resultados...", "description": "The paragraph for fetching new search results"}, "theme.SearchBar.seeAll": {"message": "Ver todos os {count} resultados"}, "theme.SearchBar.label": {"message": "Buscar", "description": "The ARIA label and placeholder for search button"}, "theme.SearchModal.searchBox.resetButtonTitle": {"message": "Limpar a busca", "description": "The label and ARIA label for search box reset button"}, "theme.SearchModal.searchBox.cancelButtonText": {"message": "<PERSON><PERSON><PERSON>", "description": "The label and ARIA label for search box cancel button"}, "theme.SearchModal.startScreen.recentSearchesTitle": {"message": "<PERSON><PERSON>", "description": "The title for recent searches"}, "theme.SearchModal.startScreen.noRecentSearchesText": {"message": "Nenhuma busca recente", "description": "The text when no recent searches"}, "theme.SearchModal.startScreen.saveRecentSearchButtonTitle": {"message": "Salvar esta busca", "description": "The label for save recent search button"}, "theme.SearchModal.startScreen.removeRecentSearchButtonTitle": {"message": "Remover esta busca do histórico", "description": "The label for remove recent search button"}, "theme.SearchModal.startScreen.favoriteSearchesTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "The title for favorite searches"}, "theme.SearchModal.startScreen.removeFavoriteSearchButtonTitle": {"message": "Remover esta busca dos favoritos", "description": "The label for remove favorite search button"}, "theme.SearchModal.errorScreen.titleText": {"message": "Não foi possível obter resultados", "description": "The title for error screen of search modal"}, "theme.SearchModal.errorScreen.helpText": {"message": "Talvez você deva verificar sua conexão de rede.", "description": "The help text for error screen of search modal"}, "theme.SearchModal.footer.selectText": {"message": "selecionar", "description": "The explanatory text of the action for the enter key"}, "theme.SearchModal.footer.selectKeyAriaLabel": {"message": "Tecla Enter", "description": "The ARIA label for the Enter key button that makes the selection"}, "theme.SearchModal.footer.navigateText": {"message": "<PERSON>gar", "description": "The explanatory text of the action for the Arrow up and Arrow down key"}, "theme.SearchModal.footer.navigateUpKeyAriaLabel": {"message": "Seta para cima", "description": "The ARIA label for the Arrow up key button that makes the navigation"}, "theme.SearchModal.footer.navigateDownKeyAriaLabel": {"message": "Seta para baixo", "description": "The ARIA label for the Arrow down key button that makes the navigation"}, "theme.SearchModal.footer.closeText": {"message": "fechar", "description": "The explanatory text of the action for Escape key"}, "theme.SearchModal.footer.closeKeyAriaLabel": {"message": "Tecla Esc", "description": "The ARIA label for the Escape key button that close the modal"}, "theme.SearchModal.footer.searchByText": {"message": "Esta busca utiliza", "description": "The text explain that the search is making by Algolia"}, "theme.SearchModal.noResultsScreen.noResultsText": {"message": "Nenhum resultado para", "description": "The text explains that there are no results for the following search"}, "theme.SearchModal.noResultsScreen.suggestedQueryText": {"message": "Tente buscar por", "description": "The text for the suggested query when no results are found for the following search"}, "theme.SearchModal.noResultsScreen.reportMissingResultsText": {"message": "Você acha que esta busca deveria retornar resultados?", "description": "The text for the question where the user thinks there are missing results"}, "theme.SearchModal.noResultsScreen.reportMissingResultsLinkText": {"message": "Nos avise.", "description": "The text for the link to report missing results"}, "theme.SearchModal.placeholder": {"message": "Buscar documentos", "description": "The placeholder of the input of the DocSearch pop-up modal"}, "theme.blog.post.plurals": {"message": "Uma postagem|{count} postagens", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} marcadas com \"{tagName}\"", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Authors", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "View All Authors", "description": "The label of the link targeting the blog authors page"}, "theme.blog.author.noPosts": {"message": "This author has not written any posts yet.", "description": "The text for authors with 0 blog post"}, "theme.contentVisibility.unlistedBanner.title": {"message": "Página não listada", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "Esta página não está listada. Mecanismos de busca não armazenarão nenhuma informação, e somente usuários que possuam o link direto poderão acessá-la", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Draft page", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "Tente novamente", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "Pular para o conteúdo principal", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "Marcadores", "description": "The title of the tag list page"}}