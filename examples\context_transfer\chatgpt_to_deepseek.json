[{"role": "user", "content": "Write a Python function to reverse a string."}, {"role": "assistant", "content": "Here's a simple Python function to reverse a string:\n\n```python\ndef reverse_string(s):\n    return s[::-1]\n\n# Example usage\nprint(reverse_string(\"hello\"))  # Output: olleh\n```\n\nThis uses Python's slice notation `[::-1]` which creates a reversed copy of the string."}, {"role": "user", "content": "Make it recursive instead."}, {"role": "assistant", "content": "Here's a recursive version of the string reversal function:\n\n```python\ndef reverse_string(s):\n    # Base case: empty string or single character\n    if len(s) <= 1:\n        return s\n    \n    # Recursive case: first character + reverse of rest\n    return reverse_string(s[1:]) + s[0]\n\n# Example usage\nprint(reverse_string(\"hello\"))  # Output: olleh\n```\n\nThis recursive approach works by:\n1. Base case: if the string is empty or has one character, return it as is\n2. Recursive case: take the first character and append it to the reverse of the remaining string"}, {"role": "user", "content": "Can you also show me how to do it iteratively with a loop?"}, {"role": "assistant", "content": "Here's an iterative version using a loop:\n\n```python\ndef reverse_string(s):\n    result = \"\"\n    for char in s:\n        result = char + result\n    return result\n\n# Example usage\nprint(reverse_string(\"hello\"))  # Output: olleh\n```\n\nOr alternatively, using a while loop:\n\n```python\ndef reverse_string(s):\n    result = \"\"\n    i = len(s) - 1\n    while i >= 0:\n        result += s[i]\n        i -= 1\n    return result\n```\n\nBoth approaches build the reversed string character by character."}, {"role": "user", "content": "Which approach is most efficient?"}, {"role": "assistant", "content": "The most efficient approach is the original slice notation `s[::-1]` because:\n\n1. **Slice notation**: O(n) time, O(n) space - Python's built-in slice operation is highly optimized\n2. **Iterative with loop**: O(n) time, O(n) space - but involves string concatenation which can be slower\n3. **Recursive**: O(n) time, O(n) space - but has function call overhead and stack space usage\n\nFor production code, I'd recommend the slice notation `s[::-1]` as it's:\n- Most readable\n- Most performant\n- Most Pythonic\n\nHowever, the recursive version is great for learning recursion concepts!"}, {"role": "user", "content": "What about memory usage? Can we do it in-place?"}, {"role": "assistant", "content": "In Python, strings are immutable, so we **cannot** reverse them in-place. However, if we convert to a list of characters, we can reverse in-place:\n\n```python\ndef reverse_string_inplace(s):\n    # Convert to list (mutable)\n    chars = list(s)\n    \n    # Reverse in-place using two pointers\n    left, right = 0, len(chars) - 1\n    while left < right:\n        chars[left], chars[right] = chars[right], chars[left]\n        left += 1\n        right -= 1\n    \n    # Convert back to string\n    return ''.join(chars)\n\n# Example usage\nprint(reverse_string_inplace(\"hello\"))  # Output: olleh\n```\n\n**Memory comparison:**\n- Slice notation: Creates new string (O(n) space)\n- In-place with list: O(n) space for the list, but modifies it in-place\n- Recursive: O(n) space due to call stack\n\nFor most practical purposes, the slice notation is still the best choice due to Python's string immutability."}]