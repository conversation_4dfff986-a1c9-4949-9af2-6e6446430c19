{"theme.ErrorPageContent.title": {"message": "<PERSON><PERSON> page a planté.", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "Retour au début de la page", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "Archive", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "Archive", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "Pagination de la liste des articles du blog", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "Nouvelles entrées", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "Anciennes entrées", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "Pagination des articles du blog", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "Article plus récent", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "Article plus ancien", "description": "The blog post button label to navigate to the older/next post"}, "theme.tags.tagsPageLink": {"message": "Voir tous les tags", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "Basculer entre le mode sombre et clair (actuellement {mode})", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "mode sombre", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "mode clair", "description": "The name for the light color mode"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "1 élément|{count} éléments", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "Fil d'Ariane", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.paginator.navAriaLabel": {"message": "Pages de documentation", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "Précédent", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "Suivant", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "Un document tagué|{count} documents tagués", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged} avec \"{tagName}\"", "description": "The title of the page for a docs tag"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "Ceci est la documentation de la prochaine version {versionLabel} de {siteTitle}.", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "Ceci est la documentation de {siteTitle} {versionLabel}, qui n'est plus activement maintenue.", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "Pour une documentation à jour, consultez la {latestVersionLink} ({versionLabel}).", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "dernière version", "description": "The label used for the latest version suggestion link label"}, "theme.docs.versionBadge.label": {"message": "Version: {versionLabel}"}, "theme.common.editThisPage": {"message": "Éditer cette page", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "Lien direct vers {heading}", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": " le {date}", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": " par {user}", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "<PERSON><PERSON><PERSON> mise à jour{atDate}{byUser}", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.NotFound.title": {"message": "Page introuvable", "description": "The title of the 404 page"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "Versions", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.tags.tagsListLabel": {"message": "Tags :", "description": "The label alongside a tag list"}, "theme.admonition.caution": {"message": "attention", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "danger", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "info", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "remarque", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "astuce", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "attention", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The ARIA label for close button of announcement bar"}, "theme.blog.sidebar.navAriaLabel": {"message": "Navigation article de blog récent", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "<PERSON><PERSON><PERSON>", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "Copier le code", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "<PERSON><PERSON><PERSON>", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "Activer/dés<PERSON>r le retour à la ligne", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "Développer la catégorie '{label}' de la barre latérale", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "Réduire la catégorie '{label}' de la barre latérale", "description": "The ARIA label to collapse the sidebar category"}, "theme.NotFound.p1": {"message": "Nous n'avons pas trouvé ce que vous recherchez.", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "Veuillez contacter le propriétaire du site qui vous a lié à l'URL d'origine et leur faire savoir que leur lien est cassé.", "description": "The 2nd paragraph of the 404 page"}, "theme.NavBar.navAriaLabel": {"message": "Main", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "<PERSON><PERSON>", "description": "The label for the mobile language switcher dropdown"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "Sur cette page", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "Lire plus", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "En savoir plus sur {title}", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "Une minute de lecture|{readingTime} minutes de lecture", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "Page d'accueil", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.navAriaLabel": {"message": "Docs sidebar", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "Réduire le menu latéral", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "Réduire le menu latéral", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "Fermer la barre de navigation", "description": "The ARIA label for close button of mobile sidebar"}, "theme.docs.sidebar.expandButtonTitle": {"message": "Déplier le menu latéral", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "Déplier le menu latéral", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "Ouvrir/fermer la barre de navigation", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← Retour au menu principal", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.SearchBar.seeAll": {"message": "Voir les {count} résultats"}, "theme.SearchPage.documentsFound.plurals": {"message": "Un document trouvé|{count} documents trouvés", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.existingResultsTitle": {"message": "Résultats de recherche pour « {query} »", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "Rechercher dans la documentation", "description": "The search page title for empty query"}, "theme.SearchPage.inputPlaceholder": {"message": "Tapez votre recherche ici", "description": "The placeholder for search page input"}, "theme.SearchPage.inputLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The ARIA label for search page input"}, "theme.SearchPage.algoliaLabel": {"message": "Recherche par Algolia", "description": "The ARIA label for Algolia mention"}, "theme.SearchPage.noResultsText": {"message": "Aucun résultat trouvé", "description": "The paragraph for empty search result"}, "theme.SearchPage.fetchingNewResults": {"message": "Chargement de nouveaux résultats...", "description": "The paragraph for fetching new search results"}, "theme.SearchBar.label": {"message": "<PERSON><PERSON><PERSON>", "description": "The ARIA label and placeholder for search button"}, "theme.SearchModal.searchBox.resetButtonTitle": {"message": "Effacer la requête", "description": "The label and ARIA label for search box reset button"}, "theme.SearchModal.searchBox.cancelButtonText": {"message": "Annuler", "description": "The label and ARIA label for search box cancel button"}, "theme.SearchModal.startScreen.recentSearchesTitle": {"message": "Récemment", "description": "The title for recent searches"}, "theme.SearchModal.startScreen.noRecentSearchesText": {"message": "Aucune recherche récente", "description": "The text when no recent searches"}, "theme.SearchModal.startScreen.saveRecentSearchButtonTitle": {"message": "Sauvegarder cette recherche", "description": "The label for save recent search button"}, "theme.SearchModal.startScreen.removeRecentSearchButtonTitle": {"message": "Supprimer cette recherche de l'historique", "description": "The label for remove recent search button"}, "theme.SearchModal.startScreen.favoriteSearchesTitle": {"message": "<PERSON><PERSON><PERSON>", "description": "The title for favorite searches"}, "theme.SearchModal.startScreen.removeFavoriteSearchButtonTitle": {"message": "Supprimer cette recherche des favoris", "description": "The label for remove favorite search button"}, "theme.SearchModal.errorScreen.titleText": {"message": "Impossible de récupérer les résultats", "description": "The title for error screen of search modal"}, "theme.SearchModal.errorScreen.helpText": {"message": "Vous pouvez vérifier votre connexion réseau.", "description": "The help text for error screen of search modal"}, "theme.SearchModal.footer.selectText": {"message": "s<PERSON><PERSON><PERSON><PERSON>", "description": "The explanatory text of the action for the enter key"}, "theme.SearchModal.footer.selectKeyAriaLabel": {"message": "<PERSON><PERSON>", "description": "The ARIA label for the Enter key button that makes the selection"}, "theme.SearchModal.footer.navigateText": {"message": "naviguer", "description": "The explanatory text of the action for the Arrow up and Arrow down key"}, "theme.SearchModal.footer.navigateUpKeyAriaLabel": {"message": "Flèche vers le haut", "description": "The ARIA label for the Arrow up key button that makes the navigation"}, "theme.SearchModal.footer.navigateDownKeyAriaLabel": {"message": "Flèche vers le bas", "description": "The ARIA label for the Arrow down key button that makes the navigation"}, "theme.SearchModal.footer.closeText": {"message": "fermer", "description": "The explanatory text of the action for Escape key"}, "theme.SearchModal.footer.closeKeyAriaLabel": {"message": "<PERSON>e E<PERSON>p", "description": "The ARIA label for the Escape key button that close the modal"}, "theme.SearchModal.footer.searchByText": {"message": "Recherche via", "description": "The text explain that the search is making by Algolia"}, "theme.SearchModal.noResultsScreen.noResultsText": {"message": "Aucun résultat pour", "description": "The text explains that there are no results for the following search"}, "theme.SearchModal.noResultsScreen.suggestedQueryText": {"message": "Essayez de chercher", "description": "The text for the suggested query when no results are found for the following search"}, "theme.SearchModal.noResultsScreen.reportMissingResultsText": {"message": "Vous pensez que cette requête doit donner des résultats ?", "description": "The text for the question where the user thinks there are missing results"}, "theme.SearchModal.noResultsScreen.reportMissingResultsLinkText": {"message": "Faites-le nous savoir.", "description": "The text for the link to report missing results"}, "theme.SearchModal.placeholder": {"message": "Rechercher des docs", "description": "The placeholder of the input of the DocSearch pop-up modal"}, "theme.blog.post.plurals": {"message": "Un article|{count} articles", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} tagués avec « {tagName} »", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Authors", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "View All Authors", "description": "The label of the link targeting the blog authors page"}, "theme.blog.author.noPosts": {"message": "This author has not written any posts yet.", "description": "The text for authors with 0 blog post"}, "theme.contentVisibility.unlistedBanner.title": {"message": "Page non répertoriée", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "Cette page n'est pas répertoriée. Les moteurs de recherche ne l'indexeront pas, et seuls les utilisateurs ayant un lien direct peuvent y accéder.", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Draft page", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "Aller au contenu principal", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "Tags", "description": "The title of the tag list page"}}