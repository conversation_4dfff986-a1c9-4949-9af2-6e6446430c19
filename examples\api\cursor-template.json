{"name": "multi-model-llm-wrapper", "description": "A unified wrapper for multiple LLM providers (OpenAI, Claude, Ollama, Hugging Face) with CLI and API interfaces", "tags": ["llm", "openai", "claude", "ollama", "huggingface", "<PERSON><PERSON><PERSON>", "cli", "api"], "install": ["pip install openai anthropic transformers fastapi uvicorn python-dotenv requests"], "entry": "main.py", "files": {"model_wrapper.py": "from typing import Optional, Dict, Any\nimport os\nimport openai\nfrom anthropic import Anthropic\nfrom transformers import AutoToken<PERSON>, AutoModelForCausalLM\nimport subprocess\n\nclass ModelWrapper:\n    def __init__(self):\n        # Load API keys from environment\n        self.openai_key = os.getenv('OPENAI_API_KEY')\n        self.claude_key = os.getenv('CLAUDE_API_KEY')\n        self.hf_token = os.getenv('HF_TOKEN')\n        \n        # Initialize clients\n        if self.openai_key:\n            openai.api_key = self.openai_key\n        if self.claude_key:\n            self.claude = Anthropic(api_key=self.claude_key)\n            \n    def query_openai(self, prompt: str) -> str:\n        response = openai.ChatCompletion.create(\n            model=\"gpt-4\",\n            messages=[{\"role\": \"user\", \"content\": prompt}]\n        )\n        return response.choices[0].message['content']\n        \n    def query_claude(self, prompt: str) -> str:\n        response = self.claude.messages.create(\n            model=\"claude-3-opus-20240229\",\n            messages=[{\"role\": \"user\", \"content\": prompt}]\n        )\n        return response.content[0].text\n        \n    def query_ollama(self, prompt: str, model: str = \"mistral\") -> str:\n        result = subprocess.run(\n            [\"ollama\", \"run\", model, prompt],\n            capture_output=True,\n            text=True\n        )\n        return result.stdout\n        \n    def query_huggingface(self, prompt: str, model_id: str = \"mistralai/Mistral-7B-v0.1\") -> str:\n        tokenizer = AutoTokenizer.from_pretrained(model_id, token=self.hf_token)\n        model = AutoModelForCausalLM.from_pretrained(model_id, token=self.hf_token)\n        \n        inputs = tokenizer(prompt, return_tensors=\"pt\")\n        outputs = model.generate(**inputs, max_length=200)\n        return tokenizer.decode(outputs[0], skip_special_tokens=True)\n        \n    def query_model(self, model: str, prompt: str, **kwargs) -> Dict[str, Any]:\n        try:\n            response = \"\"\n            if model == \"openai\":\n                response = self.query_openai(prompt)\n            elif model == \"claude\":\n                response = self.query_claude(prompt)\n            elif model == \"ollama\":\n                response = self.query_ollama(prompt, kwargs.get('ollama_model', 'mistral'))\n            elif model == \"huggingface\":\n                response = self.query_huggingface(prompt, kwargs.get('hf_model_id'))\n            else:\n                raise ValueError(f\"Unsupported model: {model}\")\n                \n            return {\n                \"status\": \"success\",\n                \"model\": model,\n                \"response\": response\n            }\n        except Exception as e:\n            return {\n                \"status\": \"error\",\n                \"model\": model,\n                \"error\": str(e)\n            }\n", "cli.py": "import argparse\nfrom model_wrapper import ModelWrapper\n\ndef main():\n    parser = argparse.ArgumentParser(description=\"Query various LLM models using CLI\")\n    parser.add_argument(\"--model\", choices=[\"openai\", \"claude\", \"ollama\", \"huggingface\"], required=True)\n    parser.add_argument(\"--prompt\", type=str, required=True)\n    parser.add_argument(\"--ollama-model\", type=str, default=\"mistral\")\n    parser.add_argument(\"--hf-model-id\", type=str, default=\"mistralai/Mistral-7B-v0.1\")\n    \n    args = parser.parse_args()\n    \n    wrapper = ModelWrapper()\n    result = wrapper.query_model(\n        model=args.model,\n        prompt=args.prompt,\n        ollama_model=args.ollama_model,\n        hf_model_id=args.hf_model_id\n    )\n    \n    if result[\"status\"] == \"success\":\n        print(f\"\\n--- {args.model.upper()} Response ---\\n\")\n        print(result[\"response\"])\n    else:\n        print(f\"\\nError: {result['error']}\")\n\nif __name__ == \"__main__\":\n    main()\n", "api.py": "from fastapi import FastAP<PERSON>, HTTPException\nfrom model_wrapper import ModelWrapper\nfrom typing import Optional\n\napp = FastAPI()\nwrapper = ModelWrapper()\n\*********(\"/query\")\nasync def query_model(\n    prompt: str,\n    model: str,\n    ollama_model: Optional[str] = \"mistral\",\n    hf_model_id: Optional[str] = \"mistralai/Mistral-7B-v0.1\"\n):\n    if model not in [\"openai\", \"claude\", \"ollama\", \"huggingface\"]:\n        raise HTTPException(status_code=400, detail=f\"Unsupported model: {model}\")\n        \n    result = wrapper.query_model(\n        model=model,\n        prompt=prompt,\n        ollama_model=ollama_model,\n        hf_model_id=hf_model_id\n    )\n    \n    if result[\"status\"] == \"error\":\n        raise HTTPException(status_code=500, detail=result[\"error\"])\n        \n    return result\n", "main.py": "import sys\nimport uvicorn\n\ndef main():\n    if len(sys.argv) < 2:\n        print(\"Usage: python main.py [cli|api]\")\n        sys.exit(1)\n        \n    mode = sys.argv[1]\n    if mode == \"cli\":\n        # Remove the mode argument for the CLI parser\n        sys.argv.pop(1)\n        from cli import main as cli_main\n        cli_main()\n    elif mode == \"api\":\n        import api\n        uvicorn.run(api.app, host=\"0.0.0.0\", port=8000)\n    else:\n        print(f\"Unknown mode: {mode}\")\n        print(\"Usage: python main.py [cli|api]\")\n        sys.exit(1)\n\nif __name__ == \"__main__\":\n    main()\n"}}