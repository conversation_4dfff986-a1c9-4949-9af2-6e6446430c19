# MultiMind Gateway Environment Variables Template
# Copy this file to .env and fill in your API keys

# OpenAI Configuration
# Get your API key from https://platform.openai.com/api-keys
OPENAI_API_KEY=
OPENAI_MODEL_NAME=gpt-3.5-turbo  # or gpt-4, gpt-4-turbo-preview, etc.

# Anthropic Configuration
# Get your API key from https://console.anthropic.com/settings/keys
ANTHROPIC_API_KEY=
ANTHROPIC_MODEL_NAME=claude-3-opus-20240229  # or claude-3-sonnet-20240229, claude-3-haiku-20240307

# Ollama Configuration
# Make sure Ollama is running locally
OLLAMA_API_BASE=http://localhost:11434
OLLAMA_MODEL_NAME=mistral  # or llama2, codellama, etc.

# Groq Configuration
# Get your API key from https://console.groq.com/keys
GROQ_API_KEY=
GROQ_MODEL_NAME=mixtral-8x7b-32768  # or llama2-70b-4096, etc.

# HuggingFace Configuration
# Get your API key from https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=
HUGGINGFACE_MODEL_NAME=mistralai/Mistral-7B-Instruct-v0.2  # or any other model from HuggingFace

# General Settings
# Choose your preferred default model
DEFAULT_MODEL=openai  # Options: openai, anthropic, ollama, groq, huggingface
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# MultiMind Gateway Settings
MULTIMIND_LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# Optional: Model-specific Settings
# Uncomment and modify these if you need custom settings for specific models

# OPENAI_MAX_TOKENS=2000
# OPENAI_TEMPERATURE=0.7

# ANTHROPIC_MAX_TOKENS=2000
# ANTHROPIC_TEMPERATURE=0.7

# OLLAMA_TIMEOUT=30
# OLLAMA_TEMPERATURE=0.7

# GROQ_MAX_TOKENS=2000
# GROQ_TEMPERATURE=0.7

# HUGGINGFACE_MAX_TOKENS=2000
# HUGGINGFACE_TEMPERATURE=0.7