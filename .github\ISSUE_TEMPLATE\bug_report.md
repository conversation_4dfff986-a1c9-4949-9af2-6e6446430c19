---
name: Bug Report
about: Create a report to help us improve
title: "[BUG] "
labels: bug
assignees: ''

---

## Bug Description
A clear and concise description of what the bug is.

## To Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Run code '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Environment
- OS: [e.g. Ubuntu 20.04, Windows 10]
- Python Version: [e.g. 3.8.10]
- MultiMind SDK Version: [e.g. 0.1.0]
- CUDA Version (if applicable): [e.g. 11.7]
- GPU Model (if applicable): [e.g. NVIDIA A100]

## Error Message
```
Paste the complete error message here
```

## Code Snippet
```python
# Add a minimal code snippet that reproduces the bug
from multimind.fine_tuning import UniPELTPlusTuner

# Your code here
```

## Additional Context
Add any other context about the problem here.

## Checklist
- [ ] I have searched the [existing issues](https://github.com/multimind-dev/multimind-sdk/issues) for similar bugs
- [ ] I have checked the [documentation](https://multimind-sdk.readthedocs.io/) for relevant information
- [ ] I have provided a minimal reproducible example
- [ ] I have included all relevant environment details
- [ ] I have added appropriate labels to this issue
