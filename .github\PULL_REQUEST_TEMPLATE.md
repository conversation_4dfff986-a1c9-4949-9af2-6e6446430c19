---
name: Pull Request
about: Use this template to submit a pull request
---

## Description
Please include a summary of the change and which issue is fixed. Also include relevant motivation and context.

Fixes # (issue)

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Refactoring
- [ ] Other (please describe):

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have checked my code and corrected any misspellings

## Screenshots (if applicable)

## Additional Context
Add any other context or information about the pull request here. 