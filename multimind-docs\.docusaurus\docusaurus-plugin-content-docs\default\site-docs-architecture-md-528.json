{"id": "architecture", "title": "Architecture", "description": "MultiMind SDK is designed with modularity and extensibility in mind. The architecture enables seamless integration of models, agents, RAG, and orchestration workflows.", "source": "@site/docs/architecture.md", "sourceDirName": ".", "slug": "/architecture", "permalink": "/docs/architecture", "draft": false, "unlisted": false, "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/develop/docs/architecture.md", "tags": [], "version": "current", "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Getting Started", "permalink": "/docs/getting-started"}, "next": {"title": "Integration Guide", "permalink": "/docs/integration-guide"}}