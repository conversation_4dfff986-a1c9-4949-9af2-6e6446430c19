"""
Example workflows for the MCP system.

This package contains example workflows demonstrating various use cases of the MCP system:
- CI/CD automation
- Code review automation
- Documentation generation
- Multi-platform issue management
- Basic workflow with <PERSON>lack and <PERSON>ra integrations
"""

from multimind.mcp.advanced_executor import AdvancedMCPExecutor
from multimind.models.base import <PERSON>LL<PERSON>
from multimind.integrations.github import Git<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from multimind.integrations.jira import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from multimind.integrations.slack import <PERSON>lack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from multimind.integrations.discord import DiscordIntegration<PERSON>andler

from .ci_cd_workflow import main as ci_cd_workflow
from .code_review_workflow import main as code_review_workflow
from .mcp_workflow import main as mcp_workflow
from .multi_integration_workflow import main as multi_integration_workflow
from .documentation_workflow import main as documentation_workflow

__all__ = [
    'ci_cd_workflow',
    'code_review_workflow',
    'mcp_workflow',
    'multi_integration_workflow',
    'documentation_workflow',
    'AdvancedMCPExecutor',
    'BaseLL<PERSON>',
    'GitHubIntegration<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>',
    '<PERSON>lack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    'DiscordIntegration<PERSON>andler'
] 