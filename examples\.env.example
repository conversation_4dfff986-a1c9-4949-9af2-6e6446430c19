# MultiMind SDK Multi-Model Wrapper Environment Variables
# Copy this file to .env and fill in your API keys

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-...  # Required for OpenAI models (gpt-3.5-turbo, gpt-4)

# Anthropic (<PERSON>) Configuration
# Get your API key from: https://console.anthropic.com/settings/keys
CLAUDE_API_KEY=sk-ant-...  # Required for Claude models (claude-3-opus, claude-3-sonnet)

# Hugging Face Configuration
# Get your token from: https://huggingface.co/settings/tokens
HF_TOKEN=hf_...  # Required for Hugging Face models
HF_CACHE_DIR=./models  # Optional: Custom cache directory for HF models

# Ollama Configuration
# Make sure Ollama is installed: https://ollama.ai/download
OLLAMA_HOST=http://localhost:11434  # Optional: Custom Ollama host
OLLAMA_DEFAULT_MODEL=mistral  # Optional: Default model for Ollama

# Model Settings
# Optional: Override default model versions
OPENAI_DEFAULT_MODEL=gpt-4  # Default OpenAI model
CLAUDE_DEFAULT_MODEL=claude-3-opus-20240229  # Default Claude model
HF_DEFAULT_MODEL=mistralai/Mistral-7B-v0.1  # Default Hugging Face model

# API Settings
# Optional: Configure API behavior
OPENAI_TIMEOUT=30  # Timeout in seconds for OpenAI API calls
CLAUDE_TIMEOUT=30  # Timeout in seconds for Claude API calls
HF_TIMEOUT=60  # Timeout in seconds for Hugging Face operations
MAX_RETRIES=3  # Maximum number of retries for API calls

# Logging Configuration
# Optional: Configure logging behavior
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=multimind.log  # Optional: Log file path

# Development Settings
# Optional: Development-specific settings
DEBUG=false  # Enable debug mode
TEST_MODE=false  # Enable test mode (uses mock responses)
MOCK_RESPONSES=false  # Use mock responses for testing

# Note: Never commit your actual .env file to version control
# This example file should be committed, but your .env file should be in .gitignore