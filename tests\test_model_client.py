import torch
import torch.nn as nn
import pytest
from multimind.client.model_client import (
    LSTMModelClient, RNNModelClient, GRUModelClient, SpaCyClient, S4Client, HyenaClient, MoEModelClient
)

class DummyTokenizer:
    def encode(self, text, return_tensors=None):
        return torch.tensor([[1, 2, 3]])
    def decode(self, ids, skip_special_tokens=True):
        return "dummy decoded"

tokenizer = DummyTokenizer()

def make_dummy_model():
    class DummyModel(nn.Module):
        def forward(self, x, hidden=None):
            return torch.randn_like(x, dtype=torch.float), None
    return DummyModel()

def test_lstm_model_client():
    model = make_dummy_model()
    torch.save(model, "dummy_lstm.pt")
    client = LSTMModelClient("dummy_lstm.pt", tokenizer)
    out = client.generate("test")
    assert isinstance(out, str)

def test_rnn_model_client():
    model = make_dummy_model()
    torch.save(model, "dummy_rnn.pt")
    client = RNNModelClient("dummy_rnn.pt", tokenizer)
    out = client.generate("test")
    assert isinstance(out, str)

def test_gru_model_client():
    model = make_dummy_model()
    torch.save(model, "dummy_gru.pt")
    client = GRUModelClient("dummy_gru.pt", tokenizer)
    out = client.generate("test")
    assert isinstance(out, str)

def test_spacy_client():
    try:
        import spacy
        nlp = spacy.blank("en")
        client = SpaCyClient(nlp)
        out = client.generate("Apple is looking at buying U.K. startup for $1 billion")
        assert isinstance(out, list)
    except ImportError:
        pytest.skip("spaCy not installed")

def test_s4_client():
    class DummyS4:
        def generate(self, input_ids):
            return input_ids
    client = S4Client(DummyS4(), tokenizer)
    out = client.generate("test")
    assert isinstance(out, torch.Tensor) or isinstance(out, str)

def test_hyena_client():
    class DummyHyena:
        def generate(self, input_ids):
            return input_ids
    client = HyenaClient(DummyHyena(), tokenizer)
    out = client.generate("test")
    assert isinstance(out, torch.Tensor) or isinstance(out, str)

def test_moe_model_client():
    class DummyClient:
        def generate(self, prompt, **kwargs):
            return "dummy"
    client = MoEModelClient({"a": DummyClient(), "b": DummyClient()}, lambda p: "a")
    out = client.generate("test")
    assert out == "dummy" 