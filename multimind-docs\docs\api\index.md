# API Reference

This section provides detailed documentation for the MultiMind SDK APIs.

- [RAG API Reference](rag-api.md): Classes and functions for Retrieval-Augmented Generation.
- [Python Client Library](client-library.md): How to use the Python client to interact with the SDK.
- [Authentication](authentication.md): API key and JWT authentication, security best practices.

See each page for code samples and detailed usage. 