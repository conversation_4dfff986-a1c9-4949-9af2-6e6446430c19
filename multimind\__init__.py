"""
MultiMind SDK - A flexible and composable SDK for building AI applications.

This SDK provides a set of tools and abstractions for building AI applications,
including memory management, model integration, and utility functions.

Core Components:
- Memory: Conversation and context management
- Models: LLM and embedding model integration
- Utils: Common utility functions

Each component is designed to be modular and composable, allowing for flexible
application design.
"""

from .memory import (
    BaseMemory,
    BufferMemory,
    SummaryMemory,
    SummaryBufferMemory,
    MemoryUtils
)

__version__ = "0.1.0"

# Core components
from multimind.config import Config
from multimind.models.base import BaseLLM
from multimind.router.router import ModelRouter
from multimind.core.multimind import MultiMind

# Agent components
from multimind.agents.agent import Agent
from multimind.agents.memory import AgentMemory
from multimind.agents.tools.calculator import CalculatorTool

# Orchestration components
from multimind.orchestration.prompt_chain import Prompt<PERSON>hain
from multimind.orchestration.task_runner import TaskRunner

# MCP components
from multimind.mcp.executor import MC<PERSON>Executor
from multimind.mcp.parser import <PERSON><PERSON><PERSON><PERSON>
from multimind.mcp.advanced_executor import AdvancedMC<PERSON><PERSON>xecutor
from multimind.mcp.api.base import MCP<PERSON>or<PERSON><PERSON><PERSON><PERSON>
from multimind.mcp.api.registry import WorkflowRegistry

# Integration handlers
from multimind.integrations.base import IntegrationHandler
from multimind.integrations.github import GitHubIntegrationHandler
from multimind.integrations.slack import SlackIntegrationHandler
from multimind.integrations.discord import DiscordIntegrationHandler
from multimind.integrations.jira import JiraIntegrationHandler

# Logging components
from multimind.multimind_logging.trace_logger import TraceLogger
from multimind.multimind_logging.usage_tracker import UsageTracker

# Model implementations
from multimind.models.claude import ClaudeModel
from multimind.models.ollama import OllamaModel
from multimind.models.openai import OpenAIModel

# Pre-built workflows
from multimind.mcp.workflows.code_review import CodeReviewWorkflow
from multimind.mcp.workflows.ci_cd import CICDWorkflow
from multimind.mcp.workflows.documentation import DocumentationWorkflow
# from multimind.mcp.workflows.multi_integration import MultiIntegrationWorkflow

__all__ = [
    # Memory
    "BaseMemory",
    "BufferMemory",
    "SummaryMemory",
    "SummaryBufferMemory",
    "MemoryUtils",
    
    # Version
    "__version__",

    # Core
    "BaseLLM",
    "ModelRouter",
    "Config",
    "MultiMind",

    # Agents
    "Agent",
    "AgentMemory",
    "CalculatorTool",

    # Orchestration
    "PromptChain",
    "TaskRunner",

    # MCP
    "MCPParser",
    "MCPExecutor",
    "AdvancedMCPExecutor",
    "MCPWorkflowAPI",
    "WorkflowRegistry",

    # Integrations
    "IntegrationHandler",
    "GitHubIntegrationHandler",
    "SlackIntegrationHandler",
    "DiscordIntegrationHandler",
    "JiraIntegrationHandler",

    # Logging
    "TraceLogger",
    "UsageTracker",

    # Models
    "OpenAIModel",
    "ClaudeModel",
    "OllamaModel",

    # Workflows
    "CodeReviewWorkflow",
    "CICDWorkflow",
    "DocumentationWorkflow",
]