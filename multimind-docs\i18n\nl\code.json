{"theme.ErrorPageContent.title": {"message": "Deze pagina is gecrasht.", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "<PERSON><PERSON> naar boven", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "<PERSON><PERSON>", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "<PERSON><PERSON>", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "Paginanavigatie blog", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "Nieuwere items", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "Oudere items", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "Paginanavigatie blog", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "Nieuwer bericht", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "<PERSON><PERSON><PERSON>", "description": "The blog post button label to navigate to the older/next post"}, "theme.tags.tagsPageLink": {"message": "Laat alle tags zien", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "<PERSON><PERSON><PERSON> tussen donkere en lichte modus (momenteel {mode})", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "donkere modus", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "lichte modus", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "Broodk<PERSON><PERSON>ls", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "1 artikel|{count} artikelen", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "Documentatie pagina", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "Vorige", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "Volgende", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "Een artikel getagd|{count} artikelen getagd", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged} met \"{tagName}\"", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "Versie: {version<PERSON>abel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "Dit is nog niet uitgegeven documentatie voor {siteTitle}, versie {versionLabel}", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "Dit is de documentatie voor {siteTitle} {versionLabel}, welke niet langer actief wordt onderhouden.", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "Voor de huidige documentatie, zie de {latestVersionLink} ({versionLabel}).", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "laatste versie", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "Bewerk deze pagina", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "Direct link naar {heading}", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": " op {date}", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": " door {user}", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "Laatst bijgewerkt{atDate}{byUser}", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "Versies", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "<PERSON><PERSON><PERSON> niet gevonden", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "Tags:", "description": "The label alongside a tag list"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "Sluiten", "description": "The ARIA label for close button of announcement bar"}, "theme.admonition.caution": {"message": "pas op", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "gevaar", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "info", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "notitie", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "tip", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "waarschuwing", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.blog.sidebar.navAriaLabel": {"message": "Navigatie recente blogitems", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "Gekopieerd", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "Kopieer code naar klembord", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "<PERSON><PERSON><PERSON>", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "Tekstterugloop in-/uitschakelen", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "Categorie zijbalk uitklappen '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "Categorie zijbalk inklappen '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "Main", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "<PERSON><PERSON>", "description": "The label for the mobile language switcher dropdown"}, "theme.NotFound.p1": {"message": "We kunnen niet vinden waar je naar op zoek bent.", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "Neem contact op met de eigenaar van de website die naar de originele URL heeft geleid en laat weten dat de link niet meer werkt.", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "Op deze pagina", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "<PERSON><PERSON> meer", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "<PERSON><PERSON> meer over {title}", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "Een minuut leestijd|{readingTime} minuten leestijd", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "Homepagina", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "Zijbalk inklappen", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "Zijbalk inklappen", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "Docs zijbalk", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "Sluit navigatiebalk", "description": "The ARIA label for close button of mobile sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← Terug naar het hoofdmenu", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "Navigatiebalk schakelen", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "Zijbalk uitklappen", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "Zijbalk uitklappen", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.SearchBar.seeAll": {"message": "Laat alle {count} resultaten zien"}, "theme.SearchPage.documentsFound.plurals": {"message": "Een document gevonden|{count} documenten gevonden", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.existingResultsTitle": {"message": "Zoekresultaten voor \"{query}\"", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "Doorzoek de documentatie", "description": "The search page title for empty query"}, "theme.SearchPage.inputPlaceholder": {"message": "<PERSON><PERSON> hier uw z<PERSON>", "description": "The placeholder for search page input"}, "theme.SearchPage.inputLabel": {"message": "<PERSON><PERSON>", "description": "The ARIA label for search page input"}, "theme.SearchPage.algoliaLabel": {"message": "Zoeken door Algolia", "description": "The ARIA label for Algolia mention"}, "theme.SearchPage.noResultsText": {"message": "<PERSON>n resultaten gevonden", "description": "The paragraph for empty search result"}, "theme.SearchPage.fetchingNewResults": {"message": "Resultaten ophalen...", "description": "The paragraph for fetching new search results"}, "theme.SearchBar.label": {"message": "<PERSON><PERSON>", "description": "The ARIA label and placeholder for search button"}, "theme.SearchModal.searchBox.resetButtonTitle": {"message": "Maak de zoekopdracht leeg", "description": "The label and ARIA label for search box reset button"}, "theme.SearchModal.searchBox.cancelButtonText": {"message": "<PERSON><PERSON><PERSON>", "description": "The label and ARIA label for search box cancel button"}, "theme.SearchModal.startScreen.recentSearchesTitle": {"message": "<PERSON><PERSON> zoekopdrachten", "description": "The title for recent searches"}, "theme.SearchModal.startScreen.noRecentSearchesText": {"message": "<PERSON><PERSON> recente zoekopdrachten", "description": "The text when no recent searches"}, "theme.SearchModal.startScreen.saveRecentSearchButtonTitle": {"message": "S<PERSON> deze zoekopdracht op", "description": "The label for save recent search button"}, "theme.SearchModal.startScreen.removeRecentSearchButtonTitle": {"message": "Verwijder deze zoekopdracht uit mijn geschiedenis", "description": "The label for remove recent search button"}, "theme.SearchModal.startScreen.favoriteSearchesTitle": {"message": "Favoriet", "description": "The title for favorite searches"}, "theme.SearchModal.startScreen.removeFavoriteSearchButtonTitle": {"message": "Verwijder deze zoekopdracht uit mijn favorieten", "description": "The label for remove favorite search button"}, "theme.SearchModal.errorScreen.titleText": {"message": "Niet in staat resultaten op te halen", "description": "The title for error screen of search modal"}, "theme.SearchModal.errorScreen.helpText": {"message": "Misschien wilt u uw netwerkverbinding controleren.", "description": "The help text for error screen of search modal"}, "theme.SearchModal.footer.selectText": {"message": "om te selecteren", "description": "The explanatory text of the action for the enter key"}, "theme.SearchModal.footer.selectKeyAriaLabel": {"message": "Enter-toets", "description": "The ARIA label for the Enter key button that makes the selection"}, "theme.SearchModal.footer.navigateText": {"message": "om te navigeren", "description": "The explanatory text of the action for the Arrow up and Arrow down key"}, "theme.SearchModal.footer.navigateUpKeyAriaLabel": {"message": "Pijltoets naar boven", "description": "The ARIA label for the Arrow up key button that makes the navigation"}, "theme.SearchModal.footer.navigateDownKeyAriaLabel": {"message": "Pijltoets naar beneden", "description": "The ARIA label for the Arrow down key button that makes the navigation"}, "theme.SearchModal.footer.closeText": {"message": "om te sluiten", "description": "The explanatory text of the action for Escape key"}, "theme.SearchModal.footer.closeKeyAriaLabel": {"message": "Escape-toets", "description": "The ARIA label for the Escape key button that close the modal"}, "theme.SearchModal.footer.searchByText": {"message": "Zoek op", "description": "The text explain that the search is making by Algolia"}, "theme.SearchModal.noResultsScreen.noResultsText": {"message": "Geen resultaten voor", "description": "The text explains that there are no results for the following search"}, "theme.SearchModal.noResultsScreen.suggestedQueryText": {"message": "Probeer om te zoeken op", "description": "The text for the suggested query when no results are found for the following search"}, "theme.SearchModal.noResultsScreen.reportMissingResultsText": {"message": "Zou deze zoekopdracht resultaten moeten opleveren?", "description": "The text for the question where the user thinks there are missing results"}, "theme.SearchModal.noResultsScreen.reportMissingResultsLinkText": {"message": "Laat het ons weten.", "description": "The text for the link to report missing results"}, "theme.SearchModal.placeholder": {"message": "Doorzoek de documentatie", "description": "The placeholder of the input of the DocSearch pop-up modal"}, "theme.blog.post.plurals": {"message": "<PERSON><PERSON> be<PERSON>|{count} berichten", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} getagd met \"{tagName}\"", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Auteurs", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "Bekijk alle auteurs", "description": "The label of the link targeting the blog authors page"}, "theme.blog.author.noPosts": {"message": "Deze auteur heeft nog geen berichten geschreven.", "description": "The text for authors with 0 blog post"}, "theme.contentVisibility.unlistedBanner.title": {"message": "Verborgen page", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "Deze pagina is verborgen. Zoekmachines indexeren deze niet en alleen gebruikers met een directe link kunnen deze openen.", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Concept pagina", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "Deze pagina is een concept. Deze zal alleen zichtbaar zijn in de ontwikkelomgeving en uitgesloten worden van de productie build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "<PERSON><PERSON><PERSON> opnieuw", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "<PERSON>a naar hoof<PERSON><PERSON>d", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "Tags", "description": "The title of the tag list page"}}