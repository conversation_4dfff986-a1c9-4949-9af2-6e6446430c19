/*
 * AUTOGENERATED - DON'T EDIT
 * Your edits in this file will be overwritten in the next build!
 * Modify the docusaurus.config.js file at your site's root instead.
 */
export default {
  "title": "MultiMind SDK",
  "tagline": "One SDK.Every AI Model.Unlimited Agents",
  "url": "https://multimind.dev",
  "baseUrl": "/",
  "organizationName": "Ai2Innovate",
  "projectName": "multimind-sdk",
  "onBrokenLinks": "throw",
  "onBrokenMarkdownLinks": "warn",
  "i18n": {
    "defaultLocale": "en",
    "locales": [
      "en",
      "fr",
      "nl",
      "pt"
    ],
    "localeConfigs": {
      "en": {
        "label": "English",
        "direction": "ltr"
      },
      "fr": {
        "label": "Français",
        "direction": "ltr"
      },
      "nl": {
        "label": "Nederlands",
        "direction": "ltr"
      },
      "pt": {
        "label": "Português",
        "direction": "ltr"
      }
    },
    "path": "i18n"
  },
  "presets": [
    [
      "classic",
      {
        "docs": {
          "sidebarPath": "/Users/<USER>/Daemongodwiz/multimind-dev/multimind-sdk/multimind-docs/sidebars.js",
          "editUrl": "https://github.com/multimind-dev/multimind-sdk/edit/develop/",
          "showLastUpdateTime": true
        },
        "blog": {
          "showReadingTime": true,
          "editUrl": "https://www.multimind.dev/blogs"
        },
        "theme": {
          "customCss": "/Users/<USER>/Daemongodwiz/multimind-dev/multimind-sdk/multimind-docs/src/css/custom.css"
        }
      }
    ]
  ],
  "themeConfig": {
    "image": "../assets/Logo-with-name-final2.png",
    "navbar": {
      "title": "MultiMind",
      "logo": {
        "alt": "MultiMind SDK Logo",
        "src": "../assets/Logo-with-name-final2.png",
        "width": 160
      },
      "items": [
        {
          "type": "docSidebar",
          "sidebarId": "tutorialSidebar",
          "position": "left",
          "label": "Documentation"
        },
        {
          "to": "/docs/features",
          "label": "Features",
          "position": "left"
        },
        {
          "to": "/docs/api",
          "label": "API Reference",
          "position": "left"
        },
        {
          "to": "/docs/architecture",
          "label": "Architecture",
          "position": "left"
        },
        {
          "href": "https://github.com/multimind-dev/multimind-sdk",
          "label": "GitHub",
          "position": "right"
        }
      ],
      "hideOnScroll": false
    },
    "footer": {
      "style": "dark",
      "links": [
        {
          "title": "Docs",
          "items": [
            {
              "label": "Getting Started",
              "to": "/docs/getting-started"
            },
            {
              "label": "API Reference",
              "to": "/docs/api"
            },
            {
              "label": "Architecture",
              "to": "/docs/architecture"
            }
          ]
        },
        {
          "title": "Community",
          "items": [
            {
              "label": "Discord",
              "href": "https://discord.gg/K64U65je7h"
            },
            {
              "label": "Twitter",
              "href": "x.com/Ai2Innovate"
            },
            {
              "label": "GitHub",
              "href": "https://github.com/multimind-dev/multimind-sdk"
            }
          ]
        },
        {
          "title": "More",
          "items": [
            {
              "label": "Blog",
              "href": "https://www.multimind.dev/blogs"
            },
            {
              "label": "GitHub",
              "href": "https://github.com/multimind-dev/multimind-sdk"
            }
          ]
        }
      ],
      "copyright": "Copyright © 2025 MultiMind. Built with Docusaurus."
    },
    "prism": {
      "theme": {
        "plain": {
          "color": "#393A34",
          "backgroundColor": "#f6f8fa"
        },
        "styles": [
          {
            "types": [
              "comment",
              "prolog",
              "doctype",
              "cdata"
            ],
            "style": {
              "color": "#999988",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "namespace"
            ],
            "style": {
              "opacity": 0.7
            }
          },
          {
            "types": [
              "string",
              "attr-value"
            ],
            "style": {
              "color": "#e3116c"
            }
          },
          {
            "types": [
              "punctuation",
              "operator"
            ],
            "style": {
              "color": "#393A34"
            }
          },
          {
            "types": [
              "entity",
              "url",
              "symbol",
              "number",
              "boolean",
              "variable",
              "constant",
              "property",
              "regex",
              "inserted"
            ],
            "style": {
              "color": "#36acaa"
            }
          },
          {
            "types": [
              "atrule",
              "keyword",
              "attr-name",
              "selector"
            ],
            "style": {
              "color": "#00a4db"
            }
          },
          {
            "types": [
              "function",
              "deleted",
              "tag"
            ],
            "style": {
              "color": "#d73a49"
            }
          },
          {
            "types": [
              "function-variable"
            ],
            "style": {
              "color": "#6f42c1"
            }
          },
          {
            "types": [
              "tag",
              "selector",
              "keyword"
            ],
            "style": {
              "color": "#00009f"
            }
          }
        ]
      },
      "darkTheme": {
        "plain": {
          "color": "#F8F8F2",
          "backgroundColor": "#282A36"
        },
        "styles": [
          {
            "types": [
              "prolog",
              "constant",
              "builtin"
            ],
            "style": {
              "color": "rgb(189, 147, 249)"
            }
          },
          {
            "types": [
              "inserted",
              "function"
            ],
            "style": {
              "color": "rgb(80, 250, 123)"
            }
          },
          {
            "types": [
              "deleted"
            ],
            "style": {
              "color": "rgb(255, 85, 85)"
            }
          },
          {
            "types": [
              "changed"
            ],
            "style": {
              "color": "rgb(255, 184, 108)"
            }
          },
          {
            "types": [
              "punctuation",
              "symbol"
            ],
            "style": {
              "color": "rgb(248, 248, 242)"
            }
          },
          {
            "types": [
              "string",
              "char",
              "tag",
              "selector"
            ],
            "style": {
              "color": "rgb(255, 121, 198)"
            }
          },
          {
            "types": [
              "keyword",
              "variable"
            ],
            "style": {
              "color": "rgb(189, 147, 249)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "comment"
            ],
            "style": {
              "color": "rgb(98, 114, 164)"
            }
          },
          {
            "types": [
              "attr-name"
            ],
            "style": {
              "color": "rgb(241, 250, 140)"
            }
          }
        ]
      },
      "additionalLanguages": [
        "python",
        "bash",
        "json"
      ],
      "magicComments": [
        {
          "className": "theme-code-block-highlighted-line",
          "line": "highlight-next-line",
          "block": {
            "start": "highlight-start",
            "end": "highlight-end"
          }
        }
      ]
    },
    "algolia": {
      "appId": "YOUR_APP_ID",
      "apiKey": "YOUR_API_KEY",
      "indexName": "multimind",
      "contextualSearch": true,
      "searchParameters": {},
      "searchPagePath": "search"
    },
    "colorMode": {
      "defaultMode": "light",
      "disableSwitch": false,
      "respectPrefersColorScheme": false
    },
    "docs": {
      "versionPersistence": "localStorage",
      "sidebar": {
        "hideable": false,
        "autoCollapseCategories": false
      }
    },
    "blog": {
      "sidebar": {
        "groupByYear": true
      }
    },
    "metadata": [],
    "tableOfContents": {
      "minHeadingLevel": 2,
      "maxHeadingLevel": 3
    }
  },
  "baseUrlIssueBanner": true,
  "future": {
    "experimental_faster": {
      "swcJsLoader": false,
      "swcJsMinimizer": false,
      "swcHtmlMinimizer": false,
      "lightningCssMinimizer": false,
      "mdxCrossCompilerCache": false,
      "rspackBundler": false
    },
    "experimental_storage": {
      "type": "localStorage",
      "namespace": false
    },
    "experimental_router": "browser"
  },
  "onBrokenAnchors": "warn",
  "onDuplicateRoutes": "warn",
  "staticDirectories": [
    "static"
  ],
  "customFields": {},
  "plugins": [],
  "themes": [],
  "scripts": [],
  "headTags": [],
  "stylesheets": [],
  "clientModules": [],
  "titleDelimiter": "|",
  "noIndex": false,
  "markdown": {
    "format": "mdx",
    "mermaid": false,
    "mdx1Compat": {
      "comments": true,
      "admonitions": true,
      "headingIds": true
    },
    "anchors": {
      "maintainCase": false
    }
  }
};
