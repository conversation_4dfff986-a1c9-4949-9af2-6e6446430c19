# Core Features

MultiMind SDK offers a rich set of features for modern AI development:

- Unified model wrappers (OpenAI, Claude, Mistral, HuggingFace, Ollama)
- Agent system with memory and tool integration
- Retrieval-Augmented Generation (RAG) engine
- Model routing and orchestration
- Fine-tuning and PEFT methods
- Usage tracking and monitoring
- CLI and framework integrations

See [Advanced Features](advanced-features.md) for more. 