# Security Policy

Thank you for helping keep MultiMind SDK and its users safe!

## Supported Versions

We release security updates for the latest major and minor versions. Please ensure you are using the latest version before reporting a vulnerability.

## Reporting a Vulnerability

If you discover a security vulnerability, please report it privately and responsibly:

- **Email:** [<EMAIL>](mailto:<EMAIL>)
- **Do not** open a public issue for security vulnerabilities.
- Provide as much detail as possible, including:
  - A description of the vulnerability
  - Steps to reproduce
  - Affected versions
  - Any relevant logs or screenshots

We will acknowledge your report within 3 business days and work with you to resolve the issue promptly. Once the vulnerability is resolved, we will coordinate a public disclosure with credit to the reporter (unless you request otherwise).

## Scope

This policy applies to the MultiMind SDK codebase and all official repositories under the [multimind-dev](https://github.com/multimindlabs) organization.

## Responsible Disclosure

We ask that you:
- Do not publicly disclose the vulnerability before it has been resolved.
- Do not exploit the vulnerability beyond what is necessary to demonstrate it.
- Act in good faith to avoid privacy violations, data destruction, or service disruption.

## Acknowledgments

We appreciate the efforts of security researchers and users who responsibly disclose vulnerabilities.

Thank you for helping make MultiMind SDK more secure! 
