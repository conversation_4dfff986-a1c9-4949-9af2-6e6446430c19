# Contributor License Agreement

## MultiMind SDK Contributor License Agreement

This Contributor License Agreement ("Agreement") is entered into by and between MultiMind Dev ("Project") and the individual or entity making contributions to the Project ("You" or "Contributor").

### 1. Definitions

- "Contribution" means any original work of authorship, including any modifications or additions to an existing work, that is intentionally submitted by You to the Project for inclusion in, or documentation of, any of the products owned or managed by the Project.
- "Project" means the MultiMind SDK project and its associated repositories.
- "Submit" means any form of electronic, verbal, or written communication sent to the Project or its representatives.

### 2. Grant of Copyright License

Subject to the terms and conditions of this Agreement, You hereby grant to the Project and to recipients of software distributed by the Project a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare derivative works of, publicly display, publicly perform, sublicense, and distribute Your Contributions and such derivative works.

### 3. Grant of Patent License

Subject to the terms and conditions of this Agreement, You hereby grant to the Project and to recipients of software distributed by the Project a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Contribution, where such license applies only to those patent claims licensable by You that are necessarily infringed by Your Contribution(s) alone or by combination of Your Contribution(s) with the Project to which such Contribution(s) was submitted.

### 4. Representations and Warranties

You represent and warrant that:

1. You are legally entitled to grant the above licenses.
2. Your Contribution is an original work of authorship.
3. Your Contribution includes complete details of any third-party license or other restriction of which you are aware and which are associated with any part of Your Contribution.
4. You have disclosed any relevant patents or other intellectual property rights that you are aware of and that might be infringed by your Contribution.

### 5. Disclaimer

UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING, YOU PROVIDE YOUR CONTRIBUTION ON AN "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE.

### 6. Termination

This Agreement will automatically terminate if You fail to comply with any of the terms and conditions of this Agreement. Upon termination, the licenses granted to the Project under this Agreement will continue in full force and effect.

### 7. Miscellaneous

1. This Agreement is the entire agreement between the parties relating to the subject matter hereof.
2. This Agreement shall be governed by and construed in accordance with the laws of the jurisdiction in which the Project is established, without regard to its conflict of law provisions.
3. If any provision of this Agreement is held to be unenforceable, such provision shall be reformed only to the extent necessary to make it enforceable.
4. The Project may assign this Agreement without restriction.

### 8. Acceptance

By submitting a Contribution to the Project, You agree to be bound by the terms of this Agreement. Your submission of a Contribution constitutes Your acceptance of all the terms and conditions of this Agreement.

### 9. Contact Information

For questions about this Agreement, please contact:
- Email: [<EMAIL>](mailto:<EMAIL>)
- GitHub: [@multimind-dev](https://github.com/multimind-dev)

---

## Corporate Contributor License Agreement

If You are making Contributions on behalf of a corporation, You must also provide:

1. The full legal name of the corporation
2. The corporation's primary business address
3. The name and title of the person authorized to sign on behalf of the corporation
4. A list of all employees who are authorized to make Contributions on behalf of the corporation

Please contact the Project maintainers at [<EMAIL>](mailto:<EMAIL>) to obtain the corporate CLA form.

---

## Individual Contributor License Agreement

If You are an individual making Contributions, You must:

1. Be at least 18 years old
2. Provide your full legal name
3. Provide a valid email address
4. Provide your GitHub username
5. Confirm that you have the authority to grant the rights in this Agreement

By signing through CLA Assistant, you agree to the terms of this Agreement.

---

*Last updated: [Current Date]*

*Version: 1.0* 