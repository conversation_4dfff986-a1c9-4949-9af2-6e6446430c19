# Introduction

Welcome to the MultiMind SDK documentation.

MultiMind SDK is a modular, Python-based toolkit for fine-tuning, orchestrating, and deploying large language models (LLMs) and agents. It provides a unified interface for model management, retrieval-augmented generation (RAG), agent orchestration, and extensible tool integration.

Key features include:
- Unified model wrappers (OpenAI, Claude, Mistral, HuggingFace, Ollama)
- Agent system with memory and tool support
- Retrieval-Augmented Generation (RAG) engine
- Modular orchestrator and workflow system
- CLI for training, evaluation, and inference
- Usage tracking and logging

For more information and source code, visit the [MultiMind SDK GitHub repository](https://github.com/multimindlabs/multimind-sdk). 