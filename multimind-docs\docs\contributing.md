# Contributing

Thank you for your interest in contributing to MultiMind SDK!

## How to Contribute

1. **Fork the repository** on GitHub.
2. **Clone your fork** locally:
   ```
   git clone https://github.com/your-username/multimind-sdk.git
   cd multimind-sdk
   ```
3. **Install dependencies**:
   ```
   pip install -e ".[dev]"
   ```
4. **Set up pre-commit hooks**:
   ```
   pre-commit install
   ```
5. **Create a new branch** for your feature or fix:
   ```
   git checkout -b my-feature
   ```
6. **Follow coding standards**:
   - Use [PEP8](https://www.python.org/dev/peps/pep-0008/) style.
   - Write clear docstrings and comments.
   - Add or update tests for your changes.
7. **Commit and push** your changes:
   ```
   git add .
   git commit -m "Describe your change"
   git push origin my-feature
   ```
8. **Open a pull request** on GitHub and describe your changes.

## Code Review
- All contributions are reviewed by maintainers.
- Please respond to feedback and update your PR as needed.

Thank you for helping improve MultiMind SDK! 